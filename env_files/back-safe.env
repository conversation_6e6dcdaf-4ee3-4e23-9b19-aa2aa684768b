PORT=4000
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_HOST=postgres
RABBITMQ_URI=amqp://guest:guest@rabbitmq:5672
TEMPLATE_URL_SUFFIX=.template.govocal.com
TEMPLATE_BUCKET=cl2-tenant-templates
MEMCACHED_HOST=memcached
DEFAULT_TYPEFORM_USER_TOKEN=somefaketypeformusertoken
CLUSTER_NAME=local
RAILS_LOG_TO_STDOUT=true
WEB_CONCURRENCY=1
VERIFICATION_COW_SSL_CERT_FILE=/run/secrets/cow_ssl_cert_file
VERIFICATION_COW_SSL_CERT_KEY_FILE=/run/secrets/cow_ssl_cert_key_file
DEFAULT_FROM_EMAIL=<EMAIL>
ACTION_MAILER_DELIVERY_METHOD=smtp
SMTP_ADDRESS=mailcatcher
SMTP_PORT=1025
AWS_EMBEDDINGS_REGION=eu-central-1
AWS_TOXICITY_DETECTION_REGION=eu-central-1
HEATMAP_CALCULATION_START_HOUR_UTC=21

# ==== Development experience configuration ====
SEED_SIZE=small
# Tenant is fetched by this host name:
OVERRIDE_HOST=localhost
# Set to `true` if you want to upload/download files to/from AWS S3:
USE_AWS_S3_IN_DEV=true
# If USE_AWS_S3_IN_DEV=false, this host is used in file URLs (find it in the code for details):
ASSET_HOST_URI=http://localhost:4000
BASE_DEV_URI=

# ==== SAFE SECRETS ====
# These are generated locally purely for development purposes and
# don't give access to any real world services or data
SECRET_KEY_BASE=140d59020f5f509c7a050fc79632a867308dafbe828492bab8e788222f9362a740feebc16dc367a7912e05a020be3a6c939486baecde07351994ce843433692e
**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
JWT_RS256_PUBLIC_KEY="-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAzQMhbbt372qwi6gFwovh\nWrgaWaeDmDm1vEqDa7vJhHIcRxOJWGNF7UUdkeeNvX2DPSkhvJbMp56vVNO4sKF8\npQCSIx9ZmXVpjkJ53e/5N4xA5wjZIdFnRJuSSm2A9mKFdALQaNauPnX2zWdlK9Ul\nuB6CPXWPS3fTL1BYQjTzUuCrS0uEULlHAcXv9jXok0G55zbs5iQvYXxNlrTbXQxd\nc8c5FXQcbaC6HxSHRGvrw5IXTjpLcu+X9qa9fQCynPBkexstlg44HhW3elr71/uX\nS5/xvMqyNQTCZdE7duKWi5AhEMQiB6/qRHW03t6f98jyvY1swr6kHeP1fxXRZzIt\nHwIDAQAB\n-----END PUBLIC KEY-----\n"
VERIFICATION_ID_CARD_SALT='$2a$10$Cu8AnxXnDwWAH0OkCBrbd.'
ADMIN_API_TOKEN=e0Gd9oCkCBIplT0Pkl0XBo0WqnROkHKFpnabXMtS7yPbs
PUBLIC_API_CLIENT_ID=42cb419a-b1f8-4600-8c4e-fd45cca4bfd9
PUBLIC_API_CLIENT_SECRET=Hx7C27lxV7Qszw-zCg9UT-GFRQuxJNffllTpeU262CGabllbyTYwOmpizCygtPIZSwg
# jemalloc is an improved malloc implementation that improves memory fragmentation and usage. These ENV vars are needed to active it.
LD_PRELOAD="libjemalloc.so.2"
MALLOC_CONF="background_thread:true,metadata_thp:auto,dirty_decay_ms:5000,muzzy_decay_ms:5000,narenas:2"
GOTENBURG_PDF_URL=http://gotenberg:3000
