FROM ruby:3.3.4-slim

RUN apt-get update && apt-get install -qq -y --no-install-recommends \
      lsb-release          \
      build-essential      \
      libpq-dev            \
      file                 \
      imagemagick          \
      ca-certificates      \
      curl                 \
      git                  \
      optipng              \
      jpegoptim            \
      pngquant             \
      libimage-exiftool-perl \
      libjemalloc2         \
      libgeos-dev          \
      libgmp3-dev          \
      netcat-openbsd       \
      shared-mime-info     \
      less                 \
      clang                \
      gnupg

# Install Postgres client:
# Import the PostgreSQL repository signing key, add the repository, and install the package.
RUN curl -fsSL https://www.postgresql.org/media/keys/ACCC4CF8.asc | gpg --yes --dearmor -o /usr/share/keyrings/postgresql-archive-keyring.gpg \
    && echo "deb [signed-by=/usr/share/keyrings/postgresql-archive-keyring.gpg] https://apt.postgresql.org/pub/repos/apt $(lsb_release -cs)-pgdg main" > /etc/apt/sources.list.d/pgdg.list \
    # Make sure the version of `postgresql-client` matches the version used by the
    # `postgres` service in `docker-compose.yml`.
    && apt-get update && apt-get install -y postgresql-client-16

ENV INSTALL_PATH /cl2_back
RUN mkdir -p $INSTALL_PATH
WORKDIR $INSTALL_PATH

ENV PATH="$PATH:/cl2_back/bin"

COPY back/Gemfile back/Gemfile.* back/.ruby-version ./
COPY back/engines ./engines
COPY back/lib/citizen_lab.rb ./lib/

COPY back/. .

EXPOSE 4000

ENTRYPOINT ["sh", "entrypoint.sh"]
CMD ["bundle", "exec", "puma", "-C", "config/puma.rb"]
