# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Files::DescriptionGenerator do
  subject(:service) { described_class.new }

  # Helper method to create a file with AI processing allowed, but without queuing a
  # description generation job.
  def create_ai_file(...)
    # If the `ai_processing_allowed` attribute is set to true at creation time,
    # the uploader will automatically enqueue a description generation job.
    create(:file, ...).tap do |file|
      file.update!(ai_processing_allowed: true)
    end
  end

  describe '.enqueue_job', :active_job_que_adapter do
    it 'does not enqueue a job for a file without AI processing allowed' do
      file = create(:file)
      expect(described_class.enqueue_job(file)).to be(false)
    end

    it 'does not enqueue a job for a file with a description' do
      file = create_ai_file(:with_description)
      expect(described_class.enqueue_job(file)).to be(false)
    end

    it 'enqueues a job for an eligible file' do
      file = create_ai_file
      expect(described_class.enqueue_job(file)).to be(true)
    end

    it 'cannot enqueue more than one job for the same file' do
      file = create_ai_file
      expect(described_class.enqueue_job(file)).to be(true)
      expect(described_class.enqueue_job(file)).to be(false)
    end
  end

  describe '#generate_descriptions!' do
    it 'generates and updates the file descriptions for a supported file format', :vcr do
      file = create_ai_file(name: 'afvalkalender.pdf')
      service.generate_descriptions!(file)

      expect(file.description_multiloc).to be_present
      expect(file.description_multiloc.keys).to match_array %w[en nl-NL fr-FR]
    end
  end

  it 'generates descriptions for a file' do
    file = create_ai_file(name: 'afvalkalender.pdf')
    service.generate_descriptions!(file)

    expect(file.description_multiloc).to be_present
    expect(file.description_multiloc.keys).to match_array %w[en nl-NL fr-FR]
  end

  it 'raises RubyLLM::BadRequestError for unsupported file', :vcr do
    file = create_ai_file(name: 'david.mp3')

    expect { service.generate_descriptions!(file) }
      .to raise_error(RubyLLM::BadRequestError)
  end

  it '...' do
    file = create_ai_file(name: 'afvalkalender.pdf')
    service.generate_descriptions!(file)
  end

  it 'blank file' do
    file = create(:file, :ai, name: 'minimal_pdf.pdf')
    service.generate_descriptions!(file)
  end

  it 'png file' do
    file = create(:file, :ai, name: 'image10.jpg')
    service.generate_descriptions!(file)
    require 'pry'
    binding.pry
  end

  it 'exe file' do
    file = create(:file, :ai, name: 'keylogger.exe')
    service.generate_descriptions!(file)
    require 'pry'
    binding.pry
  end

  it 'large file (MB)' do
    file = create(:file, :ai, name: 'Video - 2024-07-16 9_20_55 PM.mp4')
    service.generate_descriptions!(file)
    require 'pry'
    binding.pry
  end

  it 'test with schema' do
    file = create(:file, name: 'minimal_pdf.pdf')
    file.update(ai_processing_allowed: true)
    pp service.send(:generate_descriptions, file, %w[en nl-NL fr-FR de-DE es-ES])
  end
end
