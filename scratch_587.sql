update catalog
set properties =
        jsonb_set(properties, '{attributes}', (properties->'attributes') - 'is_new');




update table
set column = jsonb_set(column, '{attributes}', (column->'attributes') - 'attribute_name');


select jsonb_set(properties, '{attributes}', (properties->'attributes') - 'is_new')
from (values (jsonb '{"attributes": ["is_new", "is_old"]}')) as t(properties);

select jsonb_set(properties, '{attributes,new}', (properties->'attributes') - 'is_new')
from (values (jsonb '{"attributes": { "is_new": true, "is_old": false }}')) as t(properties);