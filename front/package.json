{"//": ["** Use this section to describe why certain dependencies are used and versioned as they are **"], "name": "cl2-front", "version": "1.0.0", "description": "CitizenLab is a ready-to-use citizen participation platform for local governments", "author": {"name": "CitizenLab"}, "main": "app/component-library/dist/index.js", "module": "app/component-library/dist/cl2-component-library.esm.js", "scripts": {"extract-intl": "tsc --skipLibCheck app/containers/App/constants && babel-node --presets @babel/env -- ./internals/scripts/extract-intl.js", "build": "NODE_OPTIONS='--max-old-space-size=5120' vite build --mode production --logLevel info", "build:test": "cross-env NODE_ENV=test NODE_OPTIONS='--max-old-space-size=5120' vite build --mode production", "start": "vite --mode development", "start:production": "rimraf ./build && npm run build && node server/production", "start:images": "cross-env NODE_ENV=production node server/images", "visualize-bundle": "vite-bundle-visualizer", "lint": "eslint --ext .js,.jsx,.ts,.tsx app --color --max-warnings=0 --fix", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --reporters=default --reporters=jest-junit -w 3 --coverage --coverageReporters=text-summary --", "cypress:open": "cypress open", "cypress:run": "cypress run --spec 'cypress/e2e/**/*.ts' --browser chrome", "majestic": "majestic --port 4231", "find-unused-messages": "node internals/scripts/find-unused-messages.js", "prettier": "prettier --write .", "detect-deadcode": "ts-prune -p app/tsconfig.json -i '(node_modules|utils/testUtils/rtl.tsx|utils/useDebugState.ts|utils/permissions|i18n|/*/*/__mocks__|vite.config.ts)' | (! grep -v 'used in module')", "dependency-cruiser": "depcruise app --validate", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build -c .storybook-build", "chromatic": "chromatic --exit-once-uploaded", "generate-component-library-types": "tsc app/component-library/index.tsx --jsx react --esModuleInterop true --skipLibCheck --emitDeclarationOnly --declaration --declarationDir app/component-library/dist", "build-component-library": "rimraf ./app/component-library/dist && npm run generate-component-library-types && rollup -c ./app/component-library/rollup.config.cjs --bundleConfigAsCjs"}, "jest-junit": {"addFileAttribute": "true"}, "majestic": {"jestScriptPath": "node_modules/jest/bin/jest.js"}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged"}}, "dependencies": {"@apollo/client": "3.12.6", "@arcgis/core": "4.28.10", "@babel/runtime": "7.x", "@craftjs/core": "0.2.11", "@esri/arcgis-rest-request": "4.2.0", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "3.1.0", "@jsonforms/core": "3.5.1", "@jsonforms/react": "3.5.1", "@jsonforms/vanilla-renderers": "3.5.1", "@segment/snippet": "5.2.1", "@sentry/react": "8.48.0", "@tanstack/react-query": "4.22.4", "@tanstack/react-virtual": "3.0.0-beta.61", "@tippyjs/react": "4.2.6", "ajv": "8.12.0", "boring-avatars": "1.7.0", "canvg": "4.0.3", "clipboard-polyfill": "4.1.0", "compression": "1.7.4", "cross-env": "7.0.3", "date-fns": "2.30.0", "dnd-core": "16.0.1", "file-saver": "2.0.5", "focus-visible": "5.2.1", "formatcoords": "1.1.3", "geojson-flatten": "1.1.1", "graphql": "16.10.0", "history": "5.3.0", "https-browserify": "1.0.0", "identity-obj-proxy": "3.0.0", "intersection-observer": "0.12.2", "intl": "1.2.5", "js-confetti": "0.11.0", "js-cookie": "2.2.1", "json-stable-stringify": "1.3.0", "jwt-decode": "3.1.2", "lodash-es": "4.17.21", "lottie-react": "2.4.0", "moment": "2.30.1", "moment-timezone": "0.5.44", "papaparse": "^5.5.3", "path-to-regexp": "6.3.0", "polished": "4.3.1", "posthog-js": "1.215.1", "process": "0.11.10", "qs": "6.10.3", "quill": "1.3.7", "quill-blot-formatter": "1.0.5", "react": "18.3.1", "react-color": "2.19.3", "react-csv": "2.2.2", "react-day-picker": "9.1.3", "react-dnd": "16.0.1", "react-dnd-html5-backend": "16.0.1", "react-dom": "18.3.1", "react-dropzone": "14.2.3", "react-easy-crop": "4.6.3", "react-focus-on": "3.9.4", "react-frame-component": "5.2.6", "react-helmet-async": "^2.0.5", "react-hook-form": "7.43.3", "react-infinite-scroller": "1.2.6", "react-intersection-observer": "9.5.2", "react-intl": "^7.0.4", "react-markdown": "^10.1.0", "react-mentions": "4.4.9", "react-password-strength-bar": "0.4.1", "react-pdf": "9.2.1", "react-range": "1.8.14", "react-resize-detector": "8.0.3", "react-router-dom": "6.3.0", "react-select": "5.9.0", "react-share": "5.1.1", "react-string-replace": "1.1.1", "react-textarea-autosize": "8.5.3", "react-transition-group": "4.4.5", "recharts": "2.12.7", "rxjs": "7.x", "shpjs": "6.0.1", "stream-http": "3.2.0", "styled-components": "5.3.9", "tippy.js": "6.3.7", "util": "0.12.5", "uuid": "^11.0.5", "vite-plugin-commonjs": "^0.10.4", "webfontloader": "1.6.28", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz", "yup": "0.32.11"}, "devDependencies": {"@babel/cli": "7.x", "@babel/core": "7.22.15", "@babel/node": "7.x", "@babel/plugin-transform-runtime": "7.23.7", "@babel/preset-env": "7.18.6", "@babel/preset-react": "7.18.6", "@babel/preset-typescript": "7.x", "@cypress/skip-test": "2.6.1", "@cypress/webpack-preprocessor": "6.0.1", "@rollup/plugin-commonjs": "25.0.7", "@rollup/plugin-node-resolve": "15.2.3", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "0.4.4", "@rollup/plugin-typescript": "9.0.2", "@sentry/vite-plugin": "^2.22.7", "@storybook/addon-viewport": "8.3.4", "@storybook/builder-vite": "8.1.6", "@storybook/react": "8.1.6", "@storybook/react-vite": "8.1.6", "@storybook/react-webpack5": "8.1.6", "@tanstack/react-query-devtools": "4.22.4", "@testing-library/jest-dom": "5.16.5", "@testing-library/react": "16.1.0", "@testing-library/user-event": "14.6.1", "@types/css-mediaquery": "0.1.4", "@types/file-saver": "2.0.5", "@types/formatcoords": "1.1.2", "@types/geojson": "7946.0.10", "@types/jest": "^29.4.0", "@types/js-cookie": "2.2.7", "@types/json-stable-stringify": "1.1.0", "@types/lodash-es": "4.17.12", "@types/node": "20.14.9", "@types/node-uuid": "0.0.29", "@types/qs": "6.9.7", "@types/quill": "2.0.10", "@types/react": "18.3.1", "@types/react-color": "3.0.12", "@types/react-datepicker": "4.19.1", "@types/react-dom": "18.3.1", "@types/react-helmet": "6.1.11", "@types/react-infinite-scroller": "1.2.3", "@types/react-jsonschema-form": "1.7.8", "@types/react-mentions": "4.1.8", "@types/react-transition-group": "4.4.5", "@types/segment-analytics": "0.0.38", "@types/shelljs": "0.8.12", "@types/shpjs": "3.4.7", "@types/styled-components": "5.1.26", "@types/webfontloader": "1.6.35", "@typescript-eslint/eslint-plugin": "6.21.0", "@typescript-eslint/parser": "6.13.2", "@vitejs/plugin-react": "^4.3.4", "@vitejs/plugin-react-refresh": "^1.3.6", "axe-core": "^4.10.2", "babel-core": "7.0.0-bridge.0", "babel-jest": "29.4.1", "babel-loader": "9.1.3", "babel-plugin-dynamic-import-node": "2.3.3", "babel-plugin-react-intl": "2.4.0", "babel-plugin-styled-components": "2.1.4", "chalk": "4.1.2", "chromatic": "10.2.1", "css-loader": "6.7.3", "css-mediaquery": "0.1.2", "cypress": "13.14.1", "cypress-axe": "^1.5.0", "cypress-circleci-reporter": "0.2.0", "cypress-file-upload": "5.0.8", "dependency-cruiser": "16.10.0", "dotenv": "^16.4.7", "esbuild-loader": "4.2.2", "eslint": "8.57.0", "eslint-plugin-import": "2.29.1", "eslint-plugin-jsx-a11y": "6.7.1", "eslint-plugin-prettier": "4.2.1", "eslint-plugin-react": "7.37.0", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-storybook": "0.8.0", "eslint-plugin-unused-imports": "3.0.0", "express": "4.21.1", "glob": "7.2.0", "husky": "4.3.8", "jest": "29.4.3", "jest-cli": "29.7.0", "jest-environment-jsdom": "29.7.0", "jest-junit": "15.0.0", "msw": "2.3.1", "msw-storybook-addon": "2.0.2", "prettier": "2.8.7", "pretty-quick": "3.1.3", "raf-stub": "3.0.0", "react-refresh": "0.14.0", "react-select-event": "5.5.1", "readline": "1.3.0", "request": "2.88.2", "resize-observer-polyfill": "1.5.1", "rimraf": "5.0.1", "rollup": "3.29.5", "rollup-plugin-dts": "6.1.1", "rollup-plugin-peer-deps-external": "2.2.4", "rollup-plugin-postcss": "4.0.2", "rollup-plugin-typescript": "1.0.1", "shelljs": "0.8.5", "storybook": "8.3.4", "storybook-react-intl": "^3.2.2", "style-loader": "3.3.3", "ts-jest": "29.2.6", "ts-loader": "9.4.4", "ts-prune": "0.10.1", "tslib": "2.6.2", "typescript": "5.3.3", "typescript-styled-plugin": "0.18.3", "vite": "^5.4.19", "vite-bundle-visualizer": "^1.2.1", "vite-plugin-checker": "^0.8.0", "vite-plugin-env-compatible": "^2.0.1", "vite-plugin-html": "^3.2.2", "vite-plugin-tsconfig-paths": "^1.4.1", "whatwg-fetch": "3.6.20", "yargs": "17.7.2"}, "optionalDependencies": {"fsevents": "2"}, "msw": {"workerDirectory": ".storybook/public"}}