export const IMAGE_MIMETYPES = new Set([
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/webp',
  'image/tiff',
  'image/svg+xml',
  'image/x-icon',
  'image/vnd.microsoft.icon',
  'image/bmp',
]);

export const VIDEO_MIMETYPES = new Set([
  'video/mp4',
  'video/webm',
  'video/ogg',
  'video/avi',
  'video/mpeg',
  'video/quicktime',
  'video/x-msvideo',
  'video/x-m4v',
]);

export const AUDIO_MIMETYPES = new Set([
  'audio/mpeg',
  'audio/mp3',
  'audio/wav',
  'audio/ogg',
  'audio/webm',
  'audio/x-mpeg',
  'audio/x-wav',
]);

export const IFRAME_MIMETYPES = new Set([
  'application/pdf',
  'text/plain',
  'text/javascript',
  'application/json',
  'application/xml',
  'text/xml',
  'application/x-yaml',
  'text/x-yaml',
  'text/html',
  'application/epub+zip',
]);

export const CSV_MIMETYPES = new Set([
  'text/csv',
  'application/csv',
  'text/comma-separated-values',
]);

export const MARKDOWN_MIMETYPES = new Set(['text/markdown']);
