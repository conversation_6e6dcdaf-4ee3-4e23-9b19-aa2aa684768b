import { defineMessages } from 'react-intl';

export default defineMessages({
  fromATemplate: {
    id: 'app.containers.AdminPage.ProjectEdit.fromATemplate',
    defaultMessage: 'From a template',
  },
  noFolder: {
    id: 'app.containers.AdminPage.ProjectEdit.noFolder',
    defaultMessage: 'No folder',
  },
  departments: {
    id: 'app.containers.AdminPage.ProjectEdit.departments',
    defaultMessage: 'Departments',
  },
  purposes: {
    id: 'app.containers.AdminPage.ProjectEdit.purposes',
    defaultMessage: 'Purposes',
  },
  searchPlaceholder: {
    id: 'app.containers.AdminPage.ProjectEdit.searchPlaceholder',
    defaultMessage: 'Search the templates',
  },
  useTemplate: {
    id: 'app.containers.AdminPage.ProjectEdit.useTemplate',
    defaultMessage: 'Use template',
  },
  moreDetails: {
    id: 'app.containers.AdminPage.ProjectEdit.moreDetails',
    defaultMessage: 'More details',
  },
  loadMoreTemplates: {
    id: 'app.containers.AdminPage.ProjectEdit.loadMoreTemplates',
    defaultMessage: 'Load more templates',
  },
  noTemplatesFound: {
    id: 'app.containers.AdminPage.ProjectEdit.noTemplatesFound',
    defaultMessage: 'No templates found',
  },
  createProject: {
    id: 'app.components.ProjectTemplatePreview.createProject',
    defaultMessage: 'Create project',
  },
  typeProjectName: {
    id: 'app.components.ProjectTemplatePreview.typeProjectName',
    defaultMessage: 'Type the name of the project',
  },
  projectTitle: {
    id: 'app.components.ProjectTemplatePreview.projectTitle',
    defaultMessage: 'The title of your project',
  },
  projectStartDate: {
    id: 'app.components.ProjectTemplatePreview.projectStartDate',
    defaultMessage: 'The start date of your project',
  },
  projectFolder: {
    id: 'app.components.ProjectTemplatePreview.projectFolder',
    defaultMessage: 'Project folder',
  },
  projectTitleError: {
    id: 'app.components.ProjectTemplatePreview.projectTitleError',
    defaultMessage: 'Please type a project title',
  },
  projectTitleMultilocError: {
    id: 'app.components.ProjectTemplatePreview.projectTitleMultilocError',
    defaultMessage: 'Please type a project title for all languages',
  },
  projectNoStartDateError: {
    id: 'app.components.ProjectTemplatePreview.projectNoStartDateError',
    defaultMessage: 'Please select a start date for the project',
  },
  projectInvalidStartDateError: {
    id: 'app.components.ProjectTemplatePreview.projectInvalidStartDateError',
    defaultMessage:
      'The selected date is invalid. Please provide a date in the following format: YYYY-MM-DD',
  },
  responseError: {
    id: 'app.components.ProjectTemplatePreview.responseError',
    defaultMessage: 'Oops, something went wrong.',
  },
  close: {
    id: 'app.components.ProjectTemplatePreview.close',
    defaultMessage: 'Close',
  },
  successMessage: {
    id: 'app.components.ProjectTemplatePreview.successMessage',
    defaultMessage: 'The project was successfully created!',
  },
  goBackTo: {
    id: 'app.components.ProjectTemplatePreview.goBackTo',
    defaultMessage: 'Go back to the {goBackLink}.',
  },
  projectsOverviewPage: {
    id: 'app.components.ProjectTemplatePreview.projectsOverviewPage',
    defaultMessage: 'projects overview page',
  },
  createProjectBasedOn: {
    id: 'app.components.ProjectTemplatePreview.createProjectBasedOn2',
    defaultMessage:
      "Create a project based on the template ''{templateTitle}''",
  },
  participationLevels: {
    id: 'app.containers.AdminPage.ProjectEdit.participationLevels',
    defaultMessage: 'Participation levels',
  },
});
