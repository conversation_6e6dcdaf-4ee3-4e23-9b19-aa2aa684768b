// moment-timezone extends the regular moment library,
// so there's no need to import both moment and moment-timezone
import moment from 'moment-timezone';

import {
  pastPresentOrFuture,
  getIsoDateUtc,
  getIsoDate,
  getIsoDateForToday,
  timeAgo,
} from './dateUtils';

// test date is 1AM June 15 2020 UTC time (Z)
const testDateStr = '2020-06-15T01:00:00Z';

// timeAgo should report the elapsed time since a given date correctly
describe('timeAgo is reported correctly', () => {
  it('should accurately return seconds passed since a date', () => {
    let date = new Date();
    date.setSeconds(date.getSeconds() - 1);
    let timeAgoResponse = timeAgo(date.valueOf(), 'en') || '';
    expect(timeAgoResponse).toEqual('1 second ago');

    date = new Date();
    date.setSeconds(date.getSeconds() - 2);
    timeAgoResponse = timeAgo(date.valueOf(), 'en') || '';
    expect(timeAgoResponse).toEqual('2 seconds ago');
  });

  it('should accurately return minutes passed since a date', () => {
    let date = new Date();
    date.setMinutes(date.getMinutes() - 1);
    let timeAgoResponse = timeAgo(date.valueOf(), 'en') || '';
    expect(timeAgoResponse).toEqual('1 minute ago');

    date = new Date();
    date.setMinutes(date.getMinutes() - 2);
    timeAgoResponse = timeAgo(date.valueOf(), 'en') || '';
    expect(timeAgoResponse).toEqual('2 minutes ago');
  });

  it('should accurately return hours passed since a date', () => {
    let date = new Date();
    date.setHours(date.getHours() - 1);
    let timeAgoResponse = timeAgo(date.valueOf(), 'en') || '';
    expect(timeAgoResponse).toEqual('1 hour ago');

    date = new Date();
    date.setHours(date.getHours() - 2);
    timeAgoResponse = timeAgo(date.valueOf(), 'en') || '';
    expect(timeAgoResponse).toEqual('2 hours ago');
  });

  it('should accurately return days passed since a date', () => {
    let date = new Date();
    date.setHours(date.getHours() - 24);
    let timeAgoResponse = timeAgo(date.valueOf(), 'en') || '';
    expect(timeAgoResponse).toEqual('1 day ago');

    date = new Date();
    date.setHours(date.getHours() - 48);
    timeAgoResponse = timeAgo(date.valueOf(), 'en') || '';
    expect(timeAgoResponse).toEqual('2 days ago');
  });

  it('should accurately return weeks passed since a date', () => {
    let date = new Date();
    date.setHours(date.getHours() - 168);
    let timeAgoResponse = timeAgo(date.valueOf(), 'en') || '';
    expect(timeAgoResponse).toEqual('1 week ago');

    date = new Date();
    date.setHours(date.getHours() - 336);
    timeAgoResponse = timeAgo(date.valueOf(), 'en') || '';
    expect(timeAgoResponse).toEqual('2 weeks ago');
  });

  it('should accurately return months passed since a date', () => {
    let date = new Date();
    date.setMonth(date.getMonth() - 1);
    let timeAgoResponse = timeAgo(date.valueOf(), 'en') || '';
    // expect(timeAgoResponse).toEqual('1 month ago'); TODO: Commented out today to fix failing test due to date.

    date = new Date();
    date.setMonth(date.getMonth() - 2);
    timeAgoResponse = timeAgo(date.valueOf(), 'en') || '';
    expect(timeAgoResponse).toEqual('2 months ago');
  });

  it('should accurately return years passed since a date', () => {
    let date = new Date();
    date.setMonth(date.getMonth() - 12);
    let timeAgoResponse = timeAgo(date.valueOf(), 'en') || '';
    expect(timeAgoResponse).toEqual('1 year ago');

    date = new Date();
    date.setMonth(date.getMonth() - 24);
    timeAgoResponse = timeAgo(date.valueOf(), 'en') || '';
    expect(timeAgoResponse).toEqual('2 years ago');
  });

  it('should accurately return time passed since a date in specified language', () => {
    let date = new Date();
    date.setMonth(date.getMonth() - 12);
    let timeAgoResponse = timeAgo(date.valueOf(), 'fr-BE') || '';
    expect(timeAgoResponse).toEqual('il y a 1 an');

    date = new Date();
    date.setMonth(date.getMonth() - 24);
    timeAgoResponse = timeAgo(date.valueOf(), 'fr-BE') || '';
    expect(timeAgoResponse).toEqual('il y a 2 ans');
  });
});

// the local time in Santiago should be June 14th 2022, 9pm,
// since Santiago is -0400 hour offset from UTC time
describe('in America/Santiago time zone', () => {
  beforeAll(() => {
    jest.useFakeTimers();
    jest.setSystemTime(new Date(testDateStr));
    moment.tz.setDefault('America/Santiago');
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  describe('general time safety checks', () => {
    it('gets the day and time correctly for the America/Santiago timezone', () => {
      expect(
        new Date().toLocaleString('en-US', { timeZone: 'America/Santiago' })
      ).toBe('6/14/2020, 9:00:00 PM');
    });
  });

  describe('getIsoDateUtc', () => {
    it('returns the correct date formatted, ignoring timezone', () => {
      expect(getIsoDateUtc('2020-06-15')).toEqual('2020-06-15');
    });
  });

  describe('getIsoDate', () => {
    it('returns the correct date formatted, taking into account user timezone', () => {
      expect(getIsoDate('2020-06-15')).toEqual('2020-06-14');
    });
  });

  describe('getIsoDateForToday', () => {
    it('returns the correct date formatted', () => {
      expect(getIsoDateForToday()).toEqual('2020-06-14');
    });
  });

  describe('pastPresentOrFuture', () => {
    describe('with a single input date', () => {
      it('should accurately report a past date', () => {
        expect(pastPresentOrFuture('2020-06-13')).toEqual('past');
      });

      it('should accurately report a present date', () => {
        expect(pastPresentOrFuture('2020-06-14')).toEqual('present');
      });

      it('should accurately report a future date', () => {
        expect(pastPresentOrFuture('2020-06-15')).toEqual('future');
      });
    });

    describe('with an input date range', () => {
      it('should accurately report a past date', () => {
        expect(pastPresentOrFuture(['2020-06-10', '2020-06-13'])).toEqual(
          'past'
        );
      });

      it('should accurately report a present date when in range', () => {
        expect(pastPresentOrFuture(['2020-06-10', '2020-06-20'])).toEqual(
          'present'
        );
      });

      it('should accurately report a present date, including start date', () => {
        expect(pastPresentOrFuture(['2020-06-14', '2020-06-20'])).toEqual(
          'present'
        );
      });

      it('should accurately report a present date, including end date', () => {
        expect(pastPresentOrFuture(['2020-06-10', '2020-06-14'])).toEqual(
          'present'
        );
      });

      it('should accurately report a present date if the end date is null', () => {
        expect(pastPresentOrFuture(['2020-06-10', null])).toEqual('present');
      });

      it('should accurately report a future date', () => {
        expect(pastPresentOrFuture(['2020-06-15', '2020-06-20'])).toEqual(
          'future'
        );
      });

      it('should accurately report a future date if the end date is null', () => {
        expect(pastPresentOrFuture(['2020-06-15', null])).toEqual('future');
      });
    });
  });
});

// the local time in Tokyo should be June 15th 2022, 10am,
// since Tokyo is +0900 hour offset from UTC time
describe('in Asia/Tokyo time zone', () => {
  beforeAll(() => {
    jest.useFakeTimers();
    jest.setSystemTime(new Date(testDateStr));
    moment.tz.setDefault('Asia/Tokyo');
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  describe('general time safety checks', () => {
    it('gets the day and time correctly for the Asia/Tokyo timezone', () => {
      expect(
        new Date().toLocaleString('en-US', { timeZone: 'Asia/Tokyo' })
      ).toBe('6/15/2020, 10:00:00 AM');
    });
  });

  describe('getIsoDateForToday', () => {
    it('returns the correct date formatted', () => {
      expect(getIsoDateForToday()).toEqual('2020-06-15');
    });
  });

  describe('getIsoDateUtc', () => {
    it('returns the correct date formatted, ignoring timezone', () => {
      expect(getIsoDateUtc('2020-06-15')).toEqual('2020-06-15');
    });
  });

  describe('getIsoDate', () => {
    it('returns the correct date formatted, taking into account user timezone', () => {
      expect(getIsoDate('2020-06-15')).toEqual('2020-06-15');
    });
  });

  describe('pastPresentOrFuture', () => {
    describe('with a single input date', () => {
      it('should accurately report a past date', () => {
        expect(pastPresentOrFuture('2020-06-14')).toEqual('past');
      });

      it('should accurately report a present date', () => {
        expect(pastPresentOrFuture('2020-06-15')).toEqual('present');
      });

      it('should accurately report a future date', () => {
        expect(pastPresentOrFuture('2020-06-16')).toEqual('future');
      });
    });

    describe('with an input date range', () => {
      it('should accurately report a past date', () => {
        expect(pastPresentOrFuture(['2020-06-10', '2020-06-14'])).toEqual(
          'past'
        );
      });

      it('should accurately report a present date when in range', () => {
        expect(pastPresentOrFuture(['2020-06-10', '2020-06-20'])).toEqual(
          'present'
        );
      });

      it('should accurately report a present date, including start date', () => {
        expect(pastPresentOrFuture(['2020-06-15', '2020-06-20'])).toEqual(
          'present'
        );
      });

      it('should accurately report a present date, including end date', () => {
        expect(pastPresentOrFuture(['2020-06-10', '2020-06-15'])).toEqual(
          'present'
        );
      });

      it('should accurately report a future date', () => {
        expect(pastPresentOrFuture(['2020-06-16', '2020-06-20'])).toEqual(
          'future'
        );
      });
    });
  });
});
