
GET http://localhost:3000/web_api/v1/app_configuration
Authorization: Bearer eyJhbGciOiJSUzI1NiJ9.eyJleHAiOjE3NTM4ODc4NzIsInN1YiI6IjM4NmQyNTVlLTJmZjEtNDE5Mi04ZTUwLWIzMDIyNTc2YmU1MCIsInJvbGVzIjpbeyJ0eXBlIjoiYWRtaW4ifV0sImNsdXN0ZXIiOiJsb2NhbCIsInRlbmFudCI6ImM3MmM1MjExLThlMDMtNDcwYi05NTY0LTA0ZWMwYThjMzIyYiJ9.CXkAyVgmDUYNOkn-CjdhfW2yNMKSDRkYo8GwBqdSkk_4V7kVVK4ZpHYlyqvnotUXTc9PpuqBwfbxQyYsnExnZ-Jv80yoRygx1LqhepS_wY_PVVtMWGwboRnTlr2akLJ_D3PptpMy8Bydh_wWzLZ0ZT1wooFMJABqN4_DV514xYUkzwT7Q86nRB7pbHk4sYMGv9X0TzNYxhej8sBHlN-FOlrRCEtFc1raw1e8_NFx7Ko4LgckuwyN2w9wmxyFJaHifpn7Qjdc38fZhat26GPowWPNi6XdfZYnGUQWnNF96wRTt8wqin3Htx6CGpSMtwKhs-TaWYTimEgFk5tzEN1BTg




###
GET http://localhost:3000/web_api/v1/files/4b8f3f6f-cde0-40a8-824b-76694f4d9102
Authorization: Bearer eyJhbGciOiJSUzI1NiJ9.eyJleHAiOjE3NTM4ODc4NzIsInN1YiI6IjM4NmQyNTVlLTJmZjEtNDE5Mi04ZTUwLWIzMDIyNTc2YmU1MCIsInJvbGVzIjpbeyJ0eXBlIjoiYWRtaW4ifV0sImNsdXN0ZXIiOiJsb2NhbCIsInRlbmFudCI6ImM3MmM1MjExLThlMDMtNDcwYi05NTY0LTA0ZWMwYThjMzIyYiJ9.CXkAyVgmDUYNOkn-CjdhfW2yNMKSDRkYo8GwBqdSkk_4V7kVVK4ZpHYlyqvnotUXTc9PpuqBwfbxQyYsnExnZ-Jv80yoRygx1LqhepS_wY_PVVtMWGwboRnTlr2akLJ_D3PptpMy8Bydh_wWzLZ0ZT1wooFMJABqN4_DV514xYUkzwT7Q86nRB7pbHk4sYMGv9X0TzNYxhej8sBHlN-FOlrRCEtFc1raw1e8_NFx7Ko4LgckuwyN2w9wmxyFJaHifpn7Qjdc38fZhat26GPowWPNi6XdfZYnGUQWnNF96wRTt8wqin3Htx6CGpSMtwKhs-TaWYTimEgFk5tzEN1BTg


###
POST .../web_api/v1/file_attachments
Content-Type: application/json

{
  "attachable_id": "6816ee35-50c4-4e63-82ad-dffeb5991e75",
  "attachable_type": "Event",
  "file_id": "b7807ee8-4137-4674-bd1f-ab21dafd37bd"
}

###
# Or alternatively I can implement it as a concern available
# to all attachable types.
POST .../web_api/v1/projects/6816ee35-50c4-4e63-82ad-dffeb5991e75/file_attachments
Content-Type: application/json

{
  "file_id": "b7807ee8-4137-4674-bd1f-ab21dafd37bd"
}

###
POST .../web_api/v1/analysis
Content-Type: application/json

{
  /* existing fields */
  "files": ['uuid-1', 'uuid-2', 'uuid-3']
}


