How to create a default value for attributes in Rails ActiveRecord model? 

# Best solution with Rails 5+

```ruby
class User < ApplicationRecord
  attribute :name, :string, default: '<PERSON>'
  attribte :active, :boolean, default: -> { '...' }
end
```

The type controls how the attribute is cast. For instance, `:boolean` (or `ActiveModel::Type::Boolean`) will cast values some strings to `false` such as: `'f'`, `0`, `'off'`.

The default can be a proc. Evaluated in the context of the model.

# Other approaches
## after_initialize

```ruby
class User < ApplicationRecord
  after_initialize :set_defaults

  def set_defaults
    self.name ||= '<PERSON>'
  end
end
```

One thing to be aware of is that this will be called every time an instance is initialized, even if it's not a new record. So, the value can be `null` in the database, but it will be set to the default value after the record is loaded.

Another variant that avoids this is:
```ruby
class User < ApplicationRecord
  after_initialize do
    break unless new_record?
    
    self.name ||= '<PERSON>'
  end
end
```

# before_validation

```ruby
class User < ApplicationRecord
  before_validation :set_defaults

  def set_defaults
    self.name ||= '<PERSON>'
  end
end
```

It all depends on when you expect the default value to be set. Other things to consider:
- It will run before every validation.
- If another callback fails, the default value might not be set.

# Other stuff

- As of Rails, there is a config that controls how