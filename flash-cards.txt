Flash cards
===========

A: Agreeing with a previous statement, with what was just said.
Q: What means "Ditto"?

As a cloze card:

Q: "I think we should go to the beach." "Ditto."


A: L'anacardier
Q: Comment s'appelle l'arbre qui produit la noix de cajou?

A: Le Vietnam, l'Inde et la Côte d'Ivoire
Q: Quels sont les trois premiers pays producteurs de noix de cajou?

A: df -h
Q: How do you check the disk space on a Unix system?

A: ary.unshift(obj, ...) → ary
Q: How do you add an element to the beginning of an array in Ruby?

A: apt-get autoremove
Q: How do you remove packages that were installed as dependencies but are no longer needed on a Debian-based system?
In Ruby, `self` is the default receiver of method invocation. There is always a `self`.


A: ^ + ⌘ + O (Intellij)
Q: How do you jump to a symbol in the current file (File structure) ?

A: Chesterton's Fence
Q: What is the name of the principle that states that you shouldn't remove a fence until you know why it was put there in the first place?

A: <PERSON>ube Goldberg-esque
Q: What is the term for a system that is overly complicated and convoluted for a simple task? (Named after a cartoonist who drew overly complicated machines)

A: Zoom escaper
Q: What is the name of the tool that plays an annoying sound to disrupt Zoom calls?

Q: What's the __repr__ equivalence in ruby?
A: object.inspect

Q: Unset an environment variable for a single command
A:
- unset VAR
- env -u VAR command (for a single command)

A: ary.delete(3)
Q: How do you delete an element from an array by value in Ruby?

A: $#
Q: Get the number of arguments passed to a shell script

A: "Associated to" would occasionally be acceptable when speaking about certain IT concepts, but in general purpose usage, "Associated with" is preferable nearly every time.
Q: "associated to" or "associated with"?

A: 2>&1
Q: Redirect stderr to stdout

A: &>filename
Q: Redirect both stdout and stderr to a file

A: docker cp <containerId>:/path/to/file /host/target/path
Q: How do you copy a file from a Docker container to the host?

A: ary.uniq { |elem| elem.attr }
Q: How do you remove duplicates from an array of objects based on a specific attribute in Ruby?

A: hash.delete(:foo) { |k| raise(KeyError, k) }
Q: How do you delete a key from a hash and raise an exception if the key doesn't exist?

Q: A pattern to destructively extract items from an array
A: _, ary = ary.partition { |elem| condition(elem) }

A: hash[:new_key] = hash.delete :old_key
Q: How do you rename a key in a hash?

A: [:a].product([:b, :c], [:d, :e])
Q: How do you get all combinations of elements from multiple arrays?

A: Model.where.missing(:association)
Q: How do you find records for which an association is not present (nil) in Rails?


Q: Difference between line buffered and block buffered?
A: Line buffered: flushes the buffer when a newline is encountered. Block buffered: flushes the buffer when it's full.


A: model.association.create(attributes)
Q: How do you add a new record to an association in Rails?

A: enum.group_by { |i| condition(i) }
Q: How do you group elements of an array by a condition?

A: ary1 & ary2
Q: How do you get the intersection of two arrays in Ruby?

A: ary1 | ary2
Q: How do you get the union of two arrays in Ruby?


A: $PROGRAM_NAME
Q: How to get the name of the script being executed?

A: ary.tally
Q: How do you count the occurrences of elements in an array?

Q: Quels sont les bienfaits de la blette ? (4)
A:
Bénéfique pour le transit intestinal (fibres)
Frein au vieillissement cellulaire (glutamine = acide aminé protéinogène -> régénération cellulaire ; antioxydants des bêta-carotènes)
Bon apport en potassium (régulation de la tension artérielle) et en fer (transport de l'oxygène)
Antihémorragique (propriétés coagulantes de la vitamine K)

A: git tag -l
Q: How do you get all tags in a Git repository?

A: git checkout tags/<tag_name> [-b <branch_name>]
Q: How do you switch to a given tag in Git?

A: Rails.application.routes.named_routes.helper_names
Q: How do you get all the helper names for routes (e.g. `user_path` or `posts_url`)?

A: set(a) <= set(b)
Q: How do you check if a list is a subset of another list in Python?

A: git rebase --onto master server client
Q: How do you rebase a branch on top of another branch, but only include the commits that are in the `client` branch but not in the `server` branch?

Riche et célèbre, en 1895 elle décide de quitter le Moulin Rouge et de se mettre à son compte dans les fêtes foraines, puis comme dompteuse. Le 6 avril, elle passe commande à Toulouse-Lautrec de panneaux décoratifs pour orner sa baraque de danseuse orientale. En décembre 1895, La Goulue accouche d'un fils, Simon Victor14, de père inconnu (« un prince », disait-elle). Un forain l'adopte et lui donne son nom. En 1898, elle se produit chez Adrien Pezon devant l'ambassadeur de Chine. Elle avait, depuis deux ou trois ans, appris à dresser les lions.

Le 10 mai 1900, à la mairie du XVIIIe arrondissement de Paris, la Goulue, dompteuse, épouse15 le prestidigitateur Joseph-Nicolas Droxler (1872-1915). Les témoins du couple sont tous issus du monde des forains. Droxler devient dompteur. Le couple habite 112, boulevard de Rochechouart (XVIIIe arrondissement).

Comme dompteuse, elle se produit dans les ménageries, fête à Neu-Neu et foire du Trône, et dans des cirques, où elle est une belluaire éminente de 1904 à 1907. Son mari et elle sont agressés par leurs fauves. Elle abandonne le domptage et réapparaît en qualité d'actrice dans des petits théâtres et même aux Bouffes du Nord. Joseph-Nicolas Droxler, dont elle s'était par ailleurs séparée sans divorcer, meurt en 1915 dans son lit, d'un problème cardiaque, tandis que son fils Simon-Victor (qu’elle surnommait « Bouton d’or ») décède en 1923, à l'âge de 27 ans, en laissant une fillette prénommée Marthe. Après cette épreuve, elle sombre dans l'alcoolisme10.

A: Louise Weber, dite La Goulue
Q: Quelle danseuse du Moulin Rouge est connue pour avoir inventé le French Cancan et s'être reconvertis en dompteuse de fauves ?

A: Ruby Toolbox
Q: What is a good resource to check the popularity and health of Ruby gems?


s the container was started without the -i, and -t options, signals are forwarded to the attached process, which means that the default CTRL-p CTRL-q detach key sequence produces no effect, but pressing CTRL-c terminates the container:

A process running as PID 1 inside a container is treated specially by Linux: it ignores any signal with the default action. So, the process doesn't terminate on SIGINT or SIGTERM unless it's coded to do so.

A: CTRL-p CTRL-q
Q: How to detach from a Docker container without stopping it?

Q: Broad categories of programmers?
A: Wet behind the ears, Sort of knows what they are doing, Experienced

Q: What typically happens to the "Sort of knows what they are doing" programmers?
A: They are offered more responsibilities and eventually transition into a leadership role. Most of them will never reach the "Experienced" level.

A: rails db:rollback STEP=n
Q: How to rollback the last n migrations?

A: rails db:migrate VERSION=20100905201547
Q: How to rollback to a target migration?

A: rails db:migrate:down VERSION=20100905201547
Q: How to rollback a single migration out of order?

A: data = File.read("/path/to/file")
data = File.open("/path/to/file").read
Q: How to read a file in Ruby?

A: Big List of Naughty Strings
Q: What is a good resource to test text input validation?

A: dig -x *******
Q: How to get the reverse DNS of an IP address?

Q: How to get the list of columns of a table in Rails?
A:
Post.column_names
# or
Post.columns.map { |column| column.name }


A:
case object
when proc1 then result1
when proc2 then result2
else default
end
Q: How to use a case statement with procs in Ruby?

Q: How to search for a string in all tables of a PostgreSQL database?
A:
$ pg_dump --data-only --inserts -U postgres your-db-name > a.tmp
$ grep United a.tmp

A: find . -type f -mtime -1
Q: How to find files modified in the last day?

A: find . -type f -mtime -1 -exec grep -l "pattern" {} +
Q: How to find files modified in the last day that contain a specific pattern?

Q: How to find files modified in the 2 hours?
A: find . -type f -mmin -120

A: sudo nginx -s stop && sudo nginx
Q: How to restart Nginx without systemd (e.g. on Mac)?

Q: What is the equivalent of python's yield in Ruby?
A:
Enumerator.new { |yielder| yielder.yield(value) }
# or
Enumerator.new { |yielder| yielder << value }


A: Nokogiri::HTML(my_html).text
Q: How to convert HTML to plain text in Ruby?

A: const sum = array.reduce((partialSum, a) => partialSum + a, 0);
Q: How to sum an array in JavaScript?


A:
- kind_of? and is_a? are synonymous.
- instance_of? is different from the other two in that it only returns true if the object is an instance of that exact class, not a subclass.
Q: What is the difference between kind_of?, is_a?, and instance_of? in Ruby?

Q: Difference between an argument and a parameter?
A: A parameter is a variable in a method definition. An argument is the actual value of this variable that gets passed to the method.

Context: "

First things first, defined? is a keyword which behaves a bit similar similar to a method. It receives the name of the thing (variable, constant, ...) to check. What makes this method different from all others is that the parser will not resolve the value of the given name but rather check directly for whether it is defined (hence the keyword property). To check if a constant is defined, you thus have to pass the actual name (rather than a Symbol):

if defined?(String)

The const_defined? on the oither hand is more regular. It expects a Symbol or String with the name of a constant and checks whether it is defined on the receiver.

Now as for the differences between the two (when used correctly): if you use them both within the context of an instance method to check for the existence of a constant, they work the same.

When running e.g. in a class definition (such that self is e.g. a class), you need to make sure to use the correct receiver for your const_defined method, e.g. if self.const_defined? :String.

Also, defined? can check for a lot more than just constants (e.g. methods, expressions, variables, ...)

If you want to use this to make sure you actually have the name of a constant at hand in a given variable, you need to use const_defined?. If you want to "statically" check whether an constant was defined, you can use defined?.
"

Q: Difference between defined? and const_defined? in Ruby?
A:
- defined? is a keyword that checks if a name is defined without resolving the value passed to it.
- const_defined? is a method that checks if a constant (given as a symbol or string) is defined on the receiver.

Q: Quels sont les ingrédients principaux (6) de la sauce Worcestershire ?
A:
- Mélasse
- Vinaigre de malt
- Anchois
- Échalote, oignons, ail
- Pulpe de tamarin
- Épices diverses (dont l'ase fétide)


A: Model.distinct.pluck(:rating)
Q: How to get unique values of a column in Rails?

A: window.location.href = '...';
Q: How to redirect to a new page in JavaScript?

A: window.location.replace('...');
Q: How to redirect to a new page in JavaScript without the ability to go back?

A: echo 12345 | rev
Q: How to reverse a string in bash?

A: myvar="some string"
   size=${#myvar}
Q: How to get the length of a string in bash?

A: Model.limit(5).order("RANDOM()")
Q: How to get random records in Rails?

A: Travel Time Map
Q: Quel est un bon outil pour visualiser la zone que l'on peut atteindre en un temps donné à partir d'un point donné ?


Q: In Ruby, what is a good use case for `and` and `or` operators?
A: Use `and` and `or` for control flow, not for boolean logic. They have lower precedence than `&&` and `||`, which can lead to unexpected behavior.


Q: How to test for overlapping date ranges in SQL?
A:
(start1, end1) OVERLAPS (start2, end2)
(start1, length1) OVERLAPS (start2, length2)


Q: définition de badine?
A: Baguette mince et flexible, servant à frapper légèrement.

Q: définition de grouillot?
A: Employé subalterne, besogneux et peu considéré. Homme à tout faire. Coursier.


A: Do not use margin in components. Use spacer components instead.
Q: What is the recommendation for managing space in components?


Context:

grep approach
To create a copy of the file without lines matching "cat" or "rat", one can use grep in reverse (-v) and with the whole-word option (-w).
grep -vwE "(cat|rat)" sourcefile > destinationfile
The whole-word option makes sure it won't match cats or grateful for example. Output redirection of your shell is used (>) to write it to a new file. We need the -E option to enable the extended regular expressions for the (one|other) syntax.
sed approach

Alternatively, to remove the lines in-place one can use sed -i:
sed -i "/\b\(cat\|rat\)\b/d" filename
The \b sets word boundaries and the d operation deletes the line matching the expression between the forward slashes. cat and rat are both being matched by the (one|other) syntax we apparently need to escape with backslashes.
Tip: use sed without the -i operator to test the output of the command before overwriting the file.


A:
grep -vwE "(cat|rat)" sourcefile > destinationfile

-v: Invert the sense of matching, to select non-matching lines.
-w: Select only those lines containing matches that form whole words.
-E: Interpret the pattern as an extended regular expression.

Q: How to remove lines containing "cat" or "rat" from a file in Unix using grep?

A:
sed -i "/\b\(cat\|rat\)\b/d" filename

-i: Edit files in place.
\b: Word boundary.
regex needs to be escaped with backslashes
Q: How to remove lines containing "cat" or "rat" from a file in Unix using sed (in-place)?


A:
grep -r 'text' path/to/directory

-r: Recursively search subdirectories listed.
Q: How to find files containing a specific text in Unix?


grep
-E  -- use extended regular expression
-F  -- use literal strings
-b  -- print the byte offset with output lines (the byte offset is the number of bytes up to the start of the match
-c  -- only print a count of matching lines
-h  -- suppress printing of filenames
-i  -- case-insensitive
-l  -- output matching files' names only
-n  -- prefix output with line numbers
-q  -- suppress normal output
-s  -- suppress messages about unreadable or non-existent files
-v  -- select non-matching lines
-w  -- force pattern to match only whole words
-x  -- force pattern to match only whole lines



Rails 5.2+

Add this to your config/environments/test.rb or whatever environment you want to have the lines in. I am testing on rails 5.

  ActiveRecord::Base.verbose_query_logs = true


A:
Add
```ruby
ActiveRecord::Base.verbose_query_logs = true
```
to your `config/environments/test.rb` file.
Q: How to log which lines of code are responsible for which SQL queries in Rails?


A:
```ruby
Idea.joins(:author).pluck('users.first_name')
```
or
```ruby
result = Idea.joins(:author).select('users.first_name')
result.map(&:first_name)
```
Q: How to select a column from a joined table in Rails?


A:
```ruby
Comment.where(created_at: 1.week.ago..Time.now)
```

Q: How filter records using a date range in Rails?


# see intermediate image
A:
By disabling BuildKit:
```bash
DOCKER_BUILDKIT=0 docker build .
```
Q: How to see the identifiers of intermediate containers in a Docker build?



A:
```ruby
[1, 2, 3, 4].to_h { |x| [x, f(x)] }
```
Q: How to create a hash from an array?


A:
```ruby
array = [2, 3]
[1,4].insert(1,*array)
```
Q: How to insert an array in the middle of another array?


A:
`undefined` means a variable is declared but not assigned a value, whereas `null` is an assigned value representing no value.
Q: What is the difference between `undefined` and `null` in JavaScript?


A:
```javascript
console.log(null === undefined) // false (not the same type)
console.log(null == undefined) // true (but the "same value")
console.log(null === null) // true (both type and value are the same)
```

A:
```bash
docker ps -a # find the container id
docker logs [container-id]
```
Q: How to see the logs of a stopped Docker container?



Context:


I tend to use the various cron configuration files as follows:

    /var/spool/cron/crontab is used by “real” users (i.e. users corresponding to humans using the system), edited using crontab -e;
    /etc/cron.d is used for package-provided cron jobs, which can run as a “system” user (e.g. logcheck for logcheck’s cron jobs); as mentioned in answers to some of your other questions on the topic, /etc/cron.d is intended for use by packages, at least on Debian-based systems;
    /etc/crontab would be used for locally-defined system jobs, run as root, except that I find /etc/cron.{hourly,daily,weekly,monthly} more convenient for those.

In my comment, by “user” I meant “human-backed user” (if you’ll allow me the expression). Jobs run as “system users”, root or otherwise, are system jobs in my mind.

From a Debian packaging perspective, Debian Policy describes the recommended practice regarding cron jobs: in summary, use /etc/cron.{hourly,daily,weekly,monthly} if appropriate, /etc/cron.d otherwise. It’s therefore normal to see package-provide jobs in all five directories.


A:
```ruby
hash = Hash.new {|hash, key| hash[key] = [] }
hash[:a] << 1

# or for an existing hash
hash.default_proc = proc {|hash, key| hash[key] = [] }
```
Q: How to set an array as the default value for a hash key?

A:
    ```bash
    git log --all -- [file path]

    // only the last commit
    git log -1 --all -- [file path]
    ```
    It will show all commits that touched the file, across all branches.

Q: How to see when a file was deleted?

A: How to find the nameservers of a domain using dig (2 ways)?
Q:
```bash
$ dig +short ns jvns.ca
art.ns.cloudflare.com.
roxy.ns.cloudflare.com.
```
or to get an always up-to-date answer, you can run
```
$ dig +trace jvns.ca
```




Dans la langue courante,
zéro
est parfois utilisé comme déterminant avec le sens de aucun, pas un seul. Le nom qui suit s'écrit donc logiquement au singulier :
J'ai fait zéro faute à la dictée.
Un câlin, ça coûte zéro euro.
On rencontre toutefois fréquemment l'accord au pluriel, lorsque le nom désigne des choses ou des personnes pouvant être diverses, afin d'insister sur cette diversité :
Il y a zéro sucres dans cette boisson. (La boisson ne contient aucun des différents types de sucres.)
Le gouvernement s'est fixé l'objectif « zéro chômeurs ». (Il n'y aura plus de chômeurs, quels qu'ils soient.)

Q: Comment accorder le nom qui suit "zéro" ?
A:
- En général, le nom qui suit "zéro" s'écrit au singulier.
- Toutefois, on peut le mettre au pluriel pour insister sur la diversité des éléments concernés (ex: "zéro sucres" pour dire qu'il n'y a aucun des différents types de sucres).



Commençons par le plus simple : devant un nom singulier, « aucun » s’écrit au singulier. Il peut néanmoins varier en genre. Exemples : « Aucun collaborateur n’était présent », « Je n’en ai aucune idée ».

Jusque-là, tout va bien, mais quand utiliser « aucuns » ? Quand il qualifie un nom qui n’existe qu’au pluriel, « aucun » prend lui-même la marque du pluriel. On écrit alors : « aucuns ».

Reste à savoir quels sont ces noms qui n’existent pas au singulier. En voici quelques-uns : archives, arrhes, fiançailles, honoraires, mœurs, représailles…, ainsi que le « trio mortuaire » condoléances, funérailles et obsèques ! On écrira donc « aucunes arrhes », « aucuns honoraires », « aucunes funérailles »…

Sans oublier « frais » ! Au sens de « dépenses », le nom frais est toujours pluriel (« faire des frais »). Ainsi, si l’on veut dire que quelque chose ne coûte rien, on écrira « aucuns frais » (de dossier, par exemple).

Autre question : comment écrire « aucun(s) travaux » ? En mettant un « s » à « aucun » ! Certes, le nom travail existe au singulier, mais il a un sens particulier au pluriel, où il désigne un ensemble d’opérations (de construction, par exemple).

C’est la même chose pour le nom vacance, lequel change de sens quand il devient vacances. On écrira, suivant le sens, « aucune vacance » (= aucun poste à pourvoir) ou « aucunes vacances » (= aucun temps libre, congé). Autres exemples : assises, ciseaux, gages, humanités, lunettes, règles, etc.

À lire également : Archives, frais, obsèques… Ces noms qui sont toujours au pluriel

Q: Quand "aucun" prend-il un "s" ?
A: "Aucun" prend un "s" quand il qualifie un nom qui n'existe qu'au pluriel. Exemples: archives, fiançailles, honoraires, mœurs, etc.

Q: Quel est le nom de la société d'architecture avec laquelle nous partageons les bureaux ?
A: AWB (Architecture Workroom Brussels)

Q:
How to remove `to_be_removed` from a jsonb column that contains something like:
```json
{ "nested": ["to_be_removed", "other"] }
{ "nested": { "to_be_removed": "...", "other": "..." }" }
```

A:
```sql
update table
set column = jsonb_set(column, '{nested}', (column->'nested') - 'to_be_removed');
```

Q:
How to validate the presence of a boolean in Rails?

A:
```ruby
class Question < ApplicationRecord
  validates :required, inclusion: [true, false]
end
```

This avoids the three-state boolean pitfall.

** How to create a generated column in Rails?**

```
create_table :users do |t|
  t.numeric :height_cm
  t.virtual :height_in, type: :numeric, as: 'height_cm / 2.54', stored: true
end
```

If stored: true, the column is materialized and can be indexed.
Otherwise it's similar to a view and is recomputed on the fly.

This type of column can be very useful for full-text search (using tsvector).


Q: How to find the source code of a method at runtime in Ruby?
A:
```ruby
object.method(:method).source_location

# If `method_source` is installed (a dependency of Rails 7+),
# you can retrieve the source directly:
object.method(:method).source

# With pry, you can also use `show-source`
show-source object.method
```

Q: What is a good alternative to a checklist?
A: A do-nothing script.

Q: Comment s'appelle le premier supermarché coopératif à Paris (le premier en Europe) ?
A: La Louve